#!/usr/bin/env python3
"""
Master-Know API 全面测试脚本
基于实际的 FastAPI OpenAPI 规范进行测试
"""

import requests
import json
import sys
import time
from typing import Dict, Any, Optional
import tempfile
import os

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class APITester:
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.test_results = []
        self.user_id = None
        self.document_id = None
        self.topic_id = None
        self.conversation_id = None
        
    def log_test(self, endpoint: str, method: str, status_code: int, success: bool, details: str = ""):
        """记录测试结果"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "details": details
        }
        self.test_results.append(result)
        status = "✅" if success else "❌"
        print(f"{status} {method} {endpoint} - {status_code} - {details}")
        
    def make_request(self, method: str, endpoint: str, **kwargs) -> requests.Response:
        """发送请求"""
        url = f"{API_BASE}{endpoint}"
        if self.access_token:
            headers = kwargs.get('headers', {})
            headers['Authorization'] = f"Bearer {self.access_token}"
            kwargs['headers'] = headers
            
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return None
            
    def test_health_endpoints(self):
        """测试健康检查端点"""
        print("\n=== 健康检查端点测试 ===")
        
        endpoints = [
            "/utils/health-check/",
            "/embedding/health",
            "/llm/health", 
            "/search/health",
            "/integration/health",
            "/integration/manticore/health"
        ]
        
        for endpoint in endpoints:
            response = self.make_request("GET", endpoint)
            if response:
                success = response.status_code == 200
                self.log_test(endpoint, "GET", response.status_code, success)
            else:
                self.log_test(endpoint, "GET", 0, False, "Request failed")
                
    def test_authentication(self):
        """测试认证相关端点"""
        print("\n=== 认证端点测试 ===")
        
        # 1. 测试登录
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        response = self.make_request("POST", "/login/access-token", data=login_data)
        if response and response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            self.log_test("/login/access-token", "POST", response.status_code, True, "Login successful")
        else:
            self.log_test("/login/access-token", "POST", response.status_code if response else 0, False, "Login failed")
            
        # 2. 测试token验证
        if self.access_token:
            response = self.make_request("POST", "/login/test-token")
            if response:
                success = response.status_code == 200
                self.log_test("/login/test-token", "POST", response.status_code, success)
                
    def test_user_endpoints(self):
        """测试用户相关端点"""
        print("\n=== 用户端点测试 ===")
        
        if not self.access_token:
            print("⚠️  需要认证token，跳过用户端点测试")
            return
            
        # 1. 获取当前用户信息
        response = self.make_request("GET", "/users/me")
        if response and response.status_code == 200:
            user_data = response.json()
            self.user_id = user_data.get("id")
            self.log_test("/users/me", "GET", response.status_code, True)
        else:
            self.log_test("/users/me", "GET", response.status_code if response else 0, False)
            
        # 2. 获取用户列表（需要管理员权限）
        response = self.make_request("GET", "/users/")
        if response:
            success = response.status_code in [200, 403]  # 403也是正常的（权限不足）
            self.log_test("/users/", "GET", response.status_code, success)
            
        # 3. 测试用户注册
        signup_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = self.make_request("POST", "/users/signup", json=signup_data)
        if response:
            success = response.status_code in [200, 201, 400]  # 400可能是用户已存在
            self.log_test("/users/signup", "POST", response.status_code, success)
            
    def test_document_endpoints(self):
        """测试文档相关端点"""
        print("\n=== 文档端点测试 ===")
        
        if not self.access_token:
            print("⚠️  需要认证token，跳过文档端点测试")
            return
            
        # 1. 创建测试文档
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("这是一个测试文档。\n包含一些测试内容用于验证文档处理功能。")
            temp_file_path = f.name
            
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('test.txt', f, 'text/plain')}
                data = {'title': 'Test Document', 'description': 'Test document for API testing'}
                
                response = self.make_request("POST", "/documents/", files=files, data=data)
                if response and response.status_code in [200, 201]:
                    doc_data = response.json()
                    self.document_id = doc_data.get("id")
                    self.log_test("/documents/", "POST", response.status_code, True, "Document created")
                else:
                    self.log_test("/documents/", "POST", response.status_code if response else 0, False)
                    
        finally:
            os.unlink(temp_file_path)
            
        # 2. 获取文档列表
        response = self.make_request("GET", "/documents/")
        if response:
            success = response.status_code == 200
            self.log_test("/documents/", "GET", response.status_code, success)
            
        # 3. 测试文档相关操作（如果有文档ID）
        if self.document_id:
            # 获取文档详情
            response = self.make_request("GET", f"/documents/{self.document_id}")
            if response:
                success = response.status_code == 200
                self.log_test(f"/documents/{self.document_id}", "GET", response.status_code, success)
                
            # 获取文档chunks
            response = self.make_request("GET", f"/documents/{self.document_id}/chunks")
            if response:
                success = response.status_code == 200
                self.log_test(f"/documents/{self.document_id}/chunks", "GET", response.status_code, success)
                
            # 处理文档
            response = self.make_request("POST", f"/documents/{self.document_id}/process")
            if response:
                success = response.status_code in [200, 202]  # 202 for async processing
                self.log_test(f"/documents/{self.document_id}/process", "POST", response.status_code, success)
                
            # 获取文档统计
            response = self.make_request("GET", f"/documents/{self.document_id}/stats")
            if response:
                success = response.status_code == 200
                self.log_test(f"/documents/{self.document_id}/stats", "GET", response.status_code, success)
                
    def test_embedding_endpoints(self):
        """测试嵌入向量端点"""
        print("\n=== 嵌入向量端点测试 ===")
        
        # 1. 单个文本嵌入
        embed_data = {"text": "这是一个测试文本"}
        response = self.make_request("POST", "/embedding/embed", json=embed_data)
        if response:
            success = response.status_code == 200
            self.log_test("/embedding/embed", "POST", response.status_code, success)
            
        # 2. 批量文本嵌入
        batch_data = {"texts": ["文本1", "文本2", "文本3"]}
        response = self.make_request("POST", "/embedding/embed/batch", json=batch_data)
        if response:
            success = response.status_code == 200
            self.log_test("/embedding/embed/batch", "POST", response.status_code, success)
            
    def test_llm_endpoints(self):
        """测试LLM端点"""
        print("\n=== LLM端点测试 ===")
        
        # 生成文本
        llm_data = {
            "prompt": "请简单介绍一下人工智能",
            "max_tokens": 100
        }
        response = self.make_request("POST", "/llm/generate", json=llm_data)
        if response:
            success = response.status_code == 200
            self.log_test("/llm/generate", "POST", response.status_code, success)
            
    def test_search_endpoints(self):
        """测试搜索端点"""
        print("\n=== 搜索端点测试 ===")
        
        # 1. 初始化搜索
        response = self.make_request("POST", "/search/initialize")
        if response:
            success = response.status_code in [200, 201]
            self.log_test("/search/initialize", "POST", response.status_code, success)
            
        # 2. 文档搜索
        search_data = {"query": "测试", "limit": 10}
        response = self.make_request("POST", "/search/documents", json=search_data)
        if response:
            success = response.status_code == 200
            self.log_test("/search/documents", "POST", response.status_code, success)
            
        # 3. 语义搜索
        response = self.make_request("POST", "/search/documents/semantic", json=search_data)
        if response:
            success = response.status_code == 200
            self.log_test("/search/documents/semantic", "POST", response.status_code, success)
            
        # 4. 搜索统计
        response = self.make_request("GET", "/search/stats")
        if response:
            success = response.status_code == 200
            self.log_test("/search/stats", "GET", response.status_code, success)
            
    def test_topic_endpoints(self):
        """测试主题相关端点"""
        print("\n=== 主题端点测试 ===")

        if not self.access_token:
            print("⚠️  需要认证token，跳过主题端点测试")
            return

        # 1. 创建主题
        topic_data = {
            "title": "测试主题",
            "description": "这是一个用于API测试的主题"
        }
        response = self.make_request("POST", "/topics/", json=topic_data)
        if response and response.status_code in [200, 201]:
            topic_data = response.json()
            self.topic_id = topic_data.get("id")
            self.log_test("/topics/", "POST", response.status_code, True, "Topic created")
        else:
            self.log_test("/topics/", "POST", response.status_code if response else 0, False)

        # 2. 获取主题列表
        response = self.make_request("GET", "/topics/")
        if response:
            success = response.status_code == 200
            self.log_test("/topics/", "GET", response.status_code, success)

        # 3. 获取用户主题统计
        response = self.make_request("GET", "/topics/users/me/stats")
        if response:
            success = response.status_code == 200
            self.log_test("/topics/users/me/stats", "GET", response.status_code, success)

        # 4. 测试主题相关操作（如果有主题ID）
        if self.topic_id:
            # 获取主题详情
            response = self.make_request("GET", f"/topics/{self.topic_id}")
            if response:
                success = response.status_code == 200
                self.log_test(f"/topics/{self.topic_id}", "GET", response.status_code, success)

            # 获取主题统计
            response = self.make_request("GET", f"/topics/{self.topic_id}/stats")
            if response:
                success = response.status_code == 200
                self.log_test(f"/topics/{self.topic_id}/stats", "GET", response.status_code, success)

            # 获取主题下的文档
            response = self.make_request("GET", f"/topics/{self.topic_id}/documents")
            if response:
                success = response.status_code == 200
                self.log_test(f"/topics/{self.topic_id}/documents", "GET", response.status_code, success)

    def test_conversation_endpoints(self):
        """测试对话相关端点"""
        print("\n=== 对话端点测试 ===")

        if not self.access_token:
            print("⚠️  需要认证token，跳过对话端点测试")
            return

        # 1. 创建对话
        conversation_data = {
            "title": "测试对话",
            "topic_id": self.topic_id if self.topic_id else None
        }
        response = self.make_request("POST", "/conversations/", json=conversation_data)
        if response and response.status_code in [200, 201]:
            conv_data = response.json()
            self.conversation_id = conv_data.get("id")
            self.log_test("/conversations/", "POST", response.status_code, True, "Conversation created")
        else:
            self.log_test("/conversations/", "POST", response.status_code if response else 0, False)

        # 2. 获取对话列表
        response = self.make_request("GET", "/conversations/")
        if response:
            success = response.status_code == 200
            self.log_test("/conversations/", "GET", response.status_code, success)

        # 3. 测试对话相关操作（如果有对话ID）
        if self.conversation_id:
            # 获取对话详情
            response = self.make_request("GET", f"/conversations/{self.conversation_id}")
            if response:
                success = response.status_code == 200
                self.log_test(f"/conversations/{self.conversation_id}", "GET", response.status_code, success)

            # 获取对话消息
            response = self.make_request("GET", f"/conversations/{self.conversation_id}/messages")
            if response:
                success = response.status_code == 200
                self.log_test(f"/conversations/{self.conversation_id}/messages", "GET", response.status_code, success)

            # 获取对话状态
            response = self.make_request("GET", f"/conversations/{self.conversation_id}/status")
            if response:
                success = response.status_code == 200
                self.log_test(f"/conversations/{self.conversation_id}/status", "GET", response.status_code, success)

            # 获取对话摘要
            response = self.make_request("GET", f"/conversations/{self.conversation_id}/summary")
            if response:
                success = response.status_code == 200
                self.log_test(f"/conversations/{self.conversation_id}/summary", "GET", response.status_code, success)

        # 4. 测试聊天端点
        chat_data = {
            "message": "你好，这是一个测试消息",
            "conversation_id": self.conversation_id
        }
        response = self.make_request("POST", "/conversations/chat", json=chat_data)
        if response:
            success = response.status_code == 200
            self.log_test("/conversations/chat", "POST", response.status_code, success)

    def test_integration_endpoints(self):
        """测试集成端点"""
        print("\n=== 集成端点测试 ===")

        # 1. Dramatiq集成测试
        response = self.make_request("POST", "/integration/dramatiq/hello")
        if response:
            success = response.status_code in [200, 202]
            self.log_test("/integration/dramatiq/hello", "POST", response.status_code, success)

        # 2. Manticore集成测试
        response = self.make_request("POST", "/integration/manticore/create-table/test_table")
        if response:
            success = response.status_code in [200, 201, 409]  # 409 if table exists
            self.log_test("/integration/manticore/create-table/test_table", "POST", response.status_code, success)

        # 3. Manticore搜索测试
        search_data = {"query": "test", "table": "test_table"}
        response = self.make_request("POST", "/integration/manticore/search", json=search_data)
        if response:
            success = response.status_code in [200, 404]  # 404 if no results
            self.log_test("/integration/manticore/search", "POST", response.status_code, success)

        # 4. 全集成测试
        response = self.make_request("POST", "/integration/test/full-integration")
        if response:
            success = response.status_code == 200
            self.log_test("/integration/test/full-integration", "POST", response.status_code, success)

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始 Master-Know API 全面测试")
        print(f"📍 测试目标: {BASE_URL}")

        # 按顺序执行测试
        self.test_health_endpoints()
        self.test_authentication()
        self.test_user_endpoints()
        self.test_document_endpoints()
        self.test_embedding_endpoints()
        self.test_llm_endpoints()
        self.test_search_endpoints()
        self.test_topic_endpoints()
        self.test_conversation_endpoints()
        self.test_integration_endpoints()

        # 输出测试总结
        self.print_summary()
        
    def print_summary(self):
        """打印测试总结"""
        print("\n" + "="*60)
        print("📊 测试总结")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests} ✅")
        print(f"失败: {failed_tests} ❌")
        print(f"成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  - {result['method']} {result['endpoint']} ({result['status_code']}) - {result['details']}")
                    
        print("\n" + "="*60)

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
