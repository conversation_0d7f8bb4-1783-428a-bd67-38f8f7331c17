#!/usr/bin/env python3
"""
Master-Know API 全面测试脚本 - 增强版
包含详细的错误处理和调试信息
"""

import requests
import json
import sys
import time
from typing import Dict, Any, Optional, List
import tempfile
import os
from urllib.parse import urljoin

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class EnhancedAPITester:
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
        self.access_token = None
        self.test_results = []
        self.user_id = None
        self.document_id = None
        self.topic_id = None
        self.conversation_id = None
        
    def log_test(self, endpoint: str, method: str, status_code: int, success: bool, 
                 details: str = "", response_data: Any = None, error: str = ""):
        """记录测试结果"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "details": details,
            "response_data": response_data,
            "error": error,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        error_info = f" | Error: {error}" if error else ""
        print(f"{status} {method} {endpoint} - {status_code} - {details}{error_info}")
        
        if response_data and not success:
            print(f"   Response: {json.dumps(response_data, indent=2)[:200]}...")
        
    def make_request(self, method: str, endpoint: str, **kwargs) -> Optional[requests.Response]:
        """发送请求并处理错误"""
        url = f"{API_BASE}{endpoint}"
        
        # 添加认证头
        if self.access_token:
            headers = kwargs.get('headers', {})
            headers['Authorization'] = f"Bearer {self.access_token}"
            kwargs['headers'] = headers
            
        try:
            print(f"🔄 {method} {endpoint}")
            response = self.session.request(method, url, **kwargs)
            return response
        except requests.exceptions.ConnectionError as e:
            self.log_test(endpoint, method, 0, False, "Connection failed", error=str(e))
            return None
        except requests.exceptions.Timeout as e:
            self.log_test(endpoint, method, 0, False, "Request timeout", error=str(e))
            return None
        except Exception as e:
            self.log_test(endpoint, method, 0, False, "Request failed", error=str(e))
            return None
            
    def test_service_health(self):
        """测试服务健康状态"""
        print("\n" + "="*60)
        print("🏥 服务健康检查")
        print("="*60)
        
        health_endpoints = [
            ("/utils/health-check/", "主服务健康检查"),
            ("/embedding/health", "嵌入服务健康检查"),
            ("/llm/health", "LLM服务健康检查"),
            ("/search/health", "搜索服务健康检查"),
            ("/integration/health", "集成服务健康检查"),
            ("/integration/manticore/health", "Manticore健康检查")
        ]
        
        for endpoint, description in health_endpoints:
            response = self.make_request("GET", endpoint)
            if response:
                try:
                    data = response.json()
                    success = response.status_code == 200
                    self.log_test(endpoint, "GET", response.status_code, success, 
                                description, data if not success else None)
                except json.JSONDecodeError:
                    self.log_test(endpoint, "GET", response.status_code, False, 
                                description, error="Invalid JSON response")
                    
    def test_authentication_flow(self):
        """测试完整的认证流程"""
        print("\n" + "="*60)
        print("🔐 认证流程测试")
        print("="*60)
        
        # 1. 测试登录
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        response = self.make_request("POST", "/login/access-token", data=login_data)
        if response:
            if response.status_code == 200:
                try:
                    data = response.json()
                    self.access_token = data.get("access_token")
                    self.log_test("/login/access-token", "POST", response.status_code, True, 
                                "登录成功", {"token_type": data.get("token_type")})
                except json.JSONDecodeError:
                    self.log_test("/login/access-token", "POST", response.status_code, False, 
                                "登录失败", error="Invalid JSON response")
            else:
                try:
                    error_data = response.json()
                    self.log_test("/login/access-token", "POST", response.status_code, False, 
                                "登录失败", error_data)
                except:
                    self.log_test("/login/access-token", "POST", response.status_code, False, 
                                "登录失败", error=response.text[:100])
                    
        # 2. 测试token验证
        if self.access_token:
            response = self.make_request("POST", "/login/test-token")
            if response:
                success = response.status_code == 200
                try:
                    data = response.json() if success else None
                    self.log_test("/login/test-token", "POST", response.status_code, success, 
                                "Token验证", data if not success else None)
                except:
                    self.log_test("/login/test-token", "POST", response.status_code, success, 
                                "Token验证")
        else:
            print("⚠️  无法获取访问token，跳过后续需要认证的测试")
            
    def test_user_management(self):
        """测试用户管理功能"""
        print("\n" + "="*60)
        print("👤 用户管理测试")
        print("="*60)
        
        if not self.access_token:
            print("⚠️  需要认证token，跳过用户管理测试")
            return
            
        # 1. 获取当前用户信息
        response = self.make_request("GET", "/users/me")
        if response:
            if response.status_code == 200:
                try:
                    user_data = response.json()
                    self.user_id = user_data.get("id")
                    self.log_test("/users/me", "GET", response.status_code, True, 
                                f"获取用户信息: {user_data.get('email', 'N/A')}")
                except json.JSONDecodeError:
                    self.log_test("/users/me", "GET", response.status_code, False, 
                                "获取用户信息失败", error="Invalid JSON")
            else:
                self.log_test("/users/me", "GET", response.status_code, False, 
                            "获取用户信息失败")
                
        # 2. 测试用户列表（可能需要管理员权限）
        response = self.make_request("GET", "/users/")
        if response:
            success = response.status_code in [200, 403]  # 403也是正常的
            status_msg = "获取用户列表成功" if response.status_code == 200 else "权限不足（正常）"
            self.log_test("/users/", "GET", response.status_code, success, status_msg)
            
        # 3. 测试用户注册
        signup_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = self.make_request("POST", "/users/signup", json=signup_data)
        if response:
            success = response.status_code in [200, 201, 400]  # 400可能是用户已存在
            try:
                data = response.json()
                if response.status_code in [200, 201]:
                    self.log_test("/users/signup", "POST", response.status_code, True, 
                                "用户注册成功")
                else:
                    self.log_test("/users/signup", "POST", response.status_code, success, 
                                "用户注册", data)
            except:
                self.log_test("/users/signup", "POST", response.status_code, success, 
                            "用户注册")
                
    def test_document_operations(self):
        """测试文档操作"""
        print("\n" + "="*60)
        print("📄 文档操作测试")
        print("="*60)
        
        if not self.access_token:
            print("⚠️  需要认证token，跳过文档操作测试")
            return
            
        # 1. 获取文档列表
        response = self.make_request("GET", "/documents/")
        if response:
            success = response.status_code == 200
            try:
                data = response.json()
                count = len(data) if isinstance(data, list) else data.get('count', 'N/A')
                self.log_test("/documents/", "GET", response.status_code, success, 
                            f"获取文档列表: {count} 个文档")
            except:
                self.log_test("/documents/", "GET", response.status_code, success, 
                            "获取文档列表")
                
        # 2. 创建测试文档
        test_content = """这是一个测试文档。

包含多行内容用于验证文档处理功能：
- 文档上传
- 文本分割
- 向量化处理
- 搜索索引

这个文档将用于测试整个文档处理流水线。"""

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
            
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('test_document.txt', f, 'text/plain')}
                data = {
                    'title': 'API测试文档',
                    'description': '用于API测试的示例文档'
                }
                
                response = self.make_request("POST", "/documents/", files=files, data=data)
                if response:
                    if response.status_code in [200, 201]:
                        try:
                            doc_data = response.json()
                            self.document_id = doc_data.get("id")
                            self.log_test("/documents/", "POST", response.status_code, True, 
                                        f"文档创建成功: ID {self.document_id}")
                        except:
                            self.log_test("/documents/", "POST", response.status_code, True, 
                                        "文档创建成功")
                    else:
                        try:
                            error_data = response.json()
                            self.log_test("/documents/", "POST", response.status_code, False, 
                                        "文档创建失败", error_data)
                        except:
                            self.log_test("/documents/", "POST", response.status_code, False, 
                                        "文档创建失败", error=response.text[:100])
                            
        finally:
            os.unlink(temp_file_path)
            
        # 3. 测试文档相关操作
        if self.document_id:
            self._test_document_details(self.document_id)
            
    def _test_document_details(self, doc_id: str):
        """测试特定文档的详细操作"""
        operations = [
            (f"/documents/{doc_id}", "GET", "获取文档详情"),
            (f"/documents/{doc_id}/chunks", "GET", "获取文档分块"),
            (f"/documents/{doc_id}/stats", "GET", "获取文档统计"),
            (f"/documents/{doc_id}/process", "POST", "处理文档"),
        ]
        
        for endpoint, method, description in operations:
            response = self.make_request(method, endpoint)
            if response:
                success = response.status_code in [200, 202]  # 202 for async
                try:
                    data = response.json() if success else None
                    self.log_test(endpoint, method, response.status_code, success, description)
                except:
                    self.log_test(endpoint, method, response.status_code, success, description)
                    
    def run_comprehensive_test(self):
        """运行全面测试"""
        print("🚀 Master-Know API 全面测试开始")
        print(f"📍 测试目标: {BASE_URL}")
        print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        # 执行测试套件
        self.test_service_health()
        self.test_authentication_flow()
        self.test_user_management()
        self.test_document_operations()
        
        # 输出测试总结
        end_time = time.time()
        self.print_comprehensive_summary(end_time - start_time)
        
    def print_comprehensive_summary(self, duration: float):
        """打印详细的测试总结"""
        print("\n" + "="*80)
        print("📊 测试总结报告")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"⏱️  测试耗时: {duration:.2f} 秒")
        print(f"📈 总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📊 成功率: {(passed_tests/total_tests*100):.1f}%")
        
        # 按状态码分组
        status_codes = {}
        for result in self.test_results:
            code = result['status_code']
            status_codes[code] = status_codes.get(code, 0) + 1
            
        print(f"\n📋 状态码分布:")
        for code, count in sorted(status_codes.items()):
            print(f"   {code}: {count} 次")
            
        if failed_tests > 0:
            print(f"\n❌ 失败的测试详情:")
            for result in self.test_results:
                if not result['success']:
                    error_info = f" - {result['error']}" if result['error'] else ""
                    print(f"   • {result['method']} {result['endpoint']} ({result['status_code']}){error_info}")
                    
        print("\n" + "="*80)
        
        # 返回测试是否整体成功
        return failed_tests == 0

if __name__ == "__main__":
    tester = EnhancedAPITester()
    success = tester.run_comprehensive_test()
    sys.exit(0 if success else 1)
