#!/usr/bin/env python3
"""
测试特定的失败端点
"""

import requests
import json
import tempfile
import os
import time

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_token():
    """获取访问token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }
    
    response = requests.post(f"{API_BASE}/login/access-token", data=login_data, timeout=10)
    if response.status_code == 200:
        return response.json().get("access_token")
    else:
        print(f"登录失败: {response.status_code} - {response.text}")
        return None

def test_document_creation_detailed():
    """详细测试文档创建"""
    print("🔍 详细测试文档创建端点")
    
    token = get_token()
    if not token:
        return
        
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建测试文件
    test_content = "这是一个测试文档，用于API测试。\n包含多行内容。"
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
        
    try:
        print(f"📄 创建的临时文件: {temp_file_path}")
        print(f"📄 文件大小: {os.path.getsize(temp_file_path)} bytes")
        
        # 测试不同的请求方式
        
        # 方式1: multipart/form-data
        print("\n📝 方式1: multipart/form-data")
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            data = {
                'title': 'Test Document',
                'description': 'Test document for API testing'
            }
            
            try:
                print(f"   发送请求到: {API_BASE}/documents/")
                print(f"   Headers: {headers}")
                print(f"   Data: {data}")
                
                response = requests.post(
                    f"{API_BASE}/documents/", 
                    files=files, 
                    data=data, 
                    headers=headers, 
                    timeout=30
                )
                
                print(f"   状态码: {response.status_code}")
                print(f"   响应头: {dict(response.headers)}")
                print(f"   响应内容: {response.text[:500]}")
                
                if response.status_code in [200, 201]:
                    print("   ✅ 文档创建成功!")
                    return response.json().get('id')
                else:
                    print("   ❌ 文档创建失败")
                    
            except requests.exceptions.Timeout:
                print("   ❌ 请求超时")
            except requests.exceptions.ConnectionError as e:
                print(f"   ❌ 连接错误: {e}")
            except Exception as e:
                print(f"   ❌ 其他错误: {e}")
                
        # 方式2: 检查端点是否存在
        print("\n📝 方式2: 检查端点")
        try:
            response = requests.get(f"{API_BASE}/documents/", headers=headers, timeout=10)
            print(f"   GET /documents/ 状态码: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(f"   现有文档数量: {len(data) if isinstance(data, list) else 'N/A'}")
        except Exception as e:
            print(f"   GET请求异常: {e}")
            
        # 方式3: 检查OPTIONS
        print("\n📝 方式3: OPTIONS请求")
        try:
            response = requests.options(f"{API_BASE}/documents/", headers=headers, timeout=10)
            print(f"   OPTIONS状态码: {response.status_code}")
            print(f"   允许的方法: {response.headers.get('Allow', 'N/A')}")
        except Exception as e:
            print(f"   OPTIONS异常: {e}")
            
    finally:
        os.unlink(temp_file_path)
        
    return None

def test_topic_creation_detailed():
    """详细测试主题创建"""
    print("\n🔍 详细测试主题创建端点")
    
    token = get_token()
    if not token:
        return
        
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 方式1: 标准JSON请求
    print("\n📝 方式1: JSON请求")
    topic_data = {
        "title": f"测试主题_{int(time.time())}",
        "description": "这是一个API测试主题"
    }
    
    try:
        print(f"   发送请求到: {API_BASE}/topics/")
        print(f"   Headers: {headers}")
        print(f"   Data: {json.dumps(topic_data, ensure_ascii=False)}")
        
        response = requests.post(
            f"{API_BASE}/topics/", 
            json=topic_data, 
            headers=headers, 
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        print(f"   响应内容: {response.text[:500]}")
        
        if response.status_code in [200, 201]:
            print("   ✅ 主题创建成功!")
            return response.json().get('id')
        else:
            print("   ❌ 主题创建失败")
            
    except requests.exceptions.Timeout:
        print("   ❌ 请求超时")
    except requests.exceptions.ConnectionError as e:
        print(f"   ❌ 连接错误: {e}")
    except Exception as e:
        print(f"   ❌ 其他错误: {e}")
        
    # 方式2: 检查GET请求
    print("\n📝 方式2: 检查GET请求")
    try:
        response = requests.get(f"{API_BASE}/topics/", headers=headers, timeout=10)
        print(f"   GET /topics/ 状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   现有主题数量: {len(data) if isinstance(data, list) else 'N/A'}")
    except Exception as e:
        print(f"   GET请求异常: {e}")
        
    # 方式3: 不同的数据格式
    print("\n📝 方式3: 简化数据")
    simple_data = {"title": "Simple Test Topic"}
    
    try:
        response = requests.post(
            f"{API_BASE}/topics/", 
            json=simple_data, 
            headers=headers, 
            timeout=30
        )
        
        print(f"   简化数据状态码: {response.status_code}")
        print(f"   简化数据响应: {response.text[:200]}")
        
    except Exception as e:
        print(f"   简化数据异常: {e}")
        
    return None

def test_other_working_endpoints():
    """测试其他工作正常的端点作为对比"""
    print("\n🔍 测试其他工作正常的端点")
    
    token = get_token()
    if not token:
        return
        
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 测试对话创建（这个应该工作）
    print("\n📝 测试对话创建")
    conv_data = {
        "title": f"测试对话_{int(time.time())}",
        "description": "测试对话"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/conversations/", 
            json=conv_data, 
            headers=headers, 
            timeout=30
        )
        
        print(f"   对话创建状态码: {response.status_code}")
        print(f"   对话创建响应: {response.text[:200]}")
        
    except Exception as e:
        print(f"   对话创建异常: {e}")
        
    # 测试用户注册（这个应该工作）
    print("\n📝 测试用户注册")
    user_data = {
        "email": f"test_{int(time.time())}@example.com",
        "password": "testpassword123",
        "full_name": "Test User"
    }
    
    try:
        response = requests.post(
            f"{API_BASE}/users/signup", 
            json=user_data, 
            headers=headers, 
            timeout=30
        )
        
        print(f"   用户注册状态码: {response.status_code}")
        print(f"   用户注册响应: {response.text[:200]}")
        
    except Exception as e:
        print(f"   用户注册异常: {e}")

def check_backend_logs():
    """检查后端日志"""
    print("\n🔍 检查后端服务日志")
    
    import subprocess
    
    try:
        result = subprocess.run(
            ["docker", "logs", "master-know-backend-1", "--tail", "20"],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("📋 最近的后端日志:")
            print(result.stdout)
            if result.stderr:
                print("📋 错误日志:")
                print(result.stderr)
        else:
            print(f"❌ 获取日志失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 获取日志异常: {e}")

if __name__ == "__main__":
    print("🚀 开始详细测试失败的端点")
    
    # 检查后端日志
    check_backend_logs()
    
    # 测试失败的端点
    doc_id = test_document_creation_detailed()
    topic_id = test_topic_creation_detailed()
    
    # 测试工作正常的端点作为对比
    test_other_working_endpoints()
    
    print("\n✅ 详细测试完成")
    
    if doc_id:
        print(f"📄 创建的文档ID: {doc_id}")
    if topic_id:
        print(f"📚 创建的主题ID: {topic_id}")
