#!/usr/bin/env python3
"""
调试失败的API端点
"""

import requests
import json
import tempfile
import os

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_access_token():
    """获取访问token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "changethis"
    }

    try:
        response = requests.post(f"{API_BASE}/login/access-token", data=login_data, timeout=10)
        print(f"🔐 登录状态码: {response.status_code}")
        if response.status_code == 200:
            token = response.json().get("access_token")
            print(f"🔐 Token获取成功: {token[:20]}..." if token else "🔐 Token为空")
            return token
        else:
            print(f"🔐 登录失败: {response.text}")
            return None
    except Exception as e:
        print(f"🔐 登录异常: {e}")
        return None

def debug_document_creation():
    """调试文档创建端点"""
    print("🔍 调试文档创建端点...")
    
    token = get_access_token()
    if not token:
        print("❌ 无法获取token")
        return
        
    headers = {"Authorization": f"Bearer {token}"}
    
    # 创建测试文件
    test_content = "这是一个测试文档，用于调试API。"
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
        f.write(test_content)
        temp_file_path = f.name
        
    try:
        # 方法1: 使用files参数
        print("📝 尝试方法1: files参数")
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test.txt', f, 'text/plain')}
            data = {'title': 'Test Document', 'description': 'Test document'}
            
            try:
                response = requests.post(f"{API_BASE}/documents/", files=files, data=data, headers=headers, timeout=30)
                print(f"   状态码: {response.status_code}")
                print(f"   响应头: {dict(response.headers)}")
                if response.text:
                    print(f"   响应内容: {response.text[:500]}")
            except Exception as e:
                print(f"   异常: {e}")
                
        # 方法2: 检查端点是否存在
        print("\n📝 检查端点可用性")
        try:
            response = requests.options(f"{API_BASE}/documents/", headers=headers, timeout=10)
            print(f"   OPTIONS状态码: {response.status_code}")
            print(f"   允许的方法: {response.headers.get('Allow', 'N/A')}")
        except Exception as e:
            print(f"   OPTIONS异常: {e}")
            
        # 方法3: 检查OpenAPI规范
        print("\n📝 检查OpenAPI规范")
        try:
            response = requests.get(f"{API_BASE}/openapi.json", timeout=10)
            if response.status_code == 200:
                openapi_data = response.json()
                if '/documents/' in openapi_data.get('paths', {}):
                    doc_path = openapi_data['paths']['/documents/']
                    print(f"   文档端点定义: {list(doc_path.keys())}")
                    if 'post' in doc_path:
                        post_def = doc_path['post']
                        print(f"   POST定义: {json.dumps(post_def.get('requestBody', {}), indent=2)}")
                else:
                    print("   文档端点未在OpenAPI中找到")
        except Exception as e:
            print(f"   OpenAPI检查异常: {e}")
            
    finally:
        os.unlink(temp_file_path)

def debug_topic_creation():
    """调试主题创建端点"""
    print("\n🔍 调试主题创建端点...")
    
    token = get_access_token()
    if not token:
        print("❌ 无法获取token")
        return
        
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    # 方法1: 标准JSON请求
    print("📝 尝试方法1: JSON请求")
    topic_data = {
        "title": "测试主题",
        "description": "API调试测试主题"
    }
    
    try:
        response = requests.post(f"{API_BASE}/topics/", json=topic_data, headers=headers, timeout=30)
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        if response.text:
            print(f"   响应内容: {response.text[:500]}")
    except Exception as e:
        print(f"   异常: {e}")
        
    # 方法2: 检查端点可用性
    print("\n📝 检查端点可用性")
    try:
        response = requests.options(f"{API_BASE}/topics/", headers=headers, timeout=10)
        print(f"   OPTIONS状态码: {response.status_code}")
        print(f"   允许的方法: {response.headers.get('Allow', 'N/A')}")
    except Exception as e:
        print(f"   OPTIONS异常: {e}")
        
    # 方法3: 检查GET是否工作
    print("\n📝 检查GET请求")
    try:
        response = requests.get(f"{API_BASE}/topics/", headers=headers, timeout=10)
        print(f"   GET状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   现有主题数量: {len(data) if isinstance(data, list) else 'N/A'}")
    except Exception as e:
        print(f"   GET异常: {e}")

def check_service_status():
    """检查服务状态"""
    print("\n🔍 检查服务状态...")
    
    # 检查基本连接
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        print(f"📖 Swagger UI: {response.status_code}")
    except Exception as e:
        print(f"📖 Swagger UI异常: {e}")
        
    # 检查API根路径
    try:
        response = requests.get(f"{API_BASE}/", timeout=5)
        print(f"🔗 API根路径: {response.status_code}")
    except Exception as e:
        print(f"🔗 API根路径异常: {e}")
        
    # 检查健康检查
    try:
        response = requests.get(f"{API_BASE}/utils/health-check/", timeout=5)
        print(f"💚 健康检查: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
    except Exception as e:
        print(f"💚 健康检查异常: {e}")

def check_all_endpoints():
    """检查所有端点的可用性"""
    print("\n🔍 检查所有端点可用性...")
    
    token = get_access_token()
    headers = {"Authorization": f"Bearer {token}"} if token else {}
    
    # 获取OpenAPI规范
    try:
        response = requests.get(f"{API_BASE}/openapi.json", timeout=10)
        if response.status_code == 200:
            openapi_data = response.json()
            paths = openapi_data.get('paths', {})
            
            print(f"📋 总端点数: {len(paths)}")
            
            # 检查每个端点的方法
            for path, methods in paths.items():
                print(f"\n📍 {path}")
                for method in methods.keys():
                    if method.upper() in ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']:
                        print(f"   {method.upper()}: 已定义")
                        
            # 特别检查失败的端点
            failed_endpoints = ['/documents/', '/topics/']
            for endpoint in failed_endpoints:
                if endpoint in paths:
                    methods = paths[endpoint]
                    print(f"\n🔍 {endpoint} 详细信息:")
                    for method, definition in methods.items():
                        if method == 'post':
                            print(f"   POST定义存在: ✅")
                            request_body = definition.get('requestBody', {})
                            if request_body:
                                content = request_body.get('content', {})
                                print(f"   支持的内容类型: {list(content.keys())}")
                        
    except Exception as e:
        print(f"OpenAPI检查异常: {e}")

if __name__ == "__main__":
    print("🚀 开始调试失败的API端点")
    
    check_service_status()
    check_all_endpoints()
    debug_document_creation()
    debug_topic_creation()
    
    print("\n✅ 调试完成")
