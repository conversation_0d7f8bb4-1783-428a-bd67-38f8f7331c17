#!/usr/bin/env python3
"""
Master-Know API 最终测试报告
生成完整的API测试报告，包括所有端点的测试结果
"""

import requests
import json
import sys
import time
from typing import Dict, Any, Optional, List
import tempfile
import os
from datetime import datetime

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class FinalAPITestReport:
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
        self.access_token = None
        self.test_results = []
        self.start_time = time.time()
        
    def authenticate(self):
        """认证获取token"""
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        try:
            response = self.session.post(f"{API_BASE}/login/access-token", data=login_data)
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get("access_token")
                return True
            else:
                print(f"❌ 认证失败: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ 认证异常: {e}")
            return False
            
    def make_request(self, method: str, endpoint: str, **kwargs) -> Optional[requests.Response]:
        """发送请求"""
        url = f"{API_BASE}{endpoint}"
        
        if self.access_token:
            headers = kwargs.get('headers', {})
            headers['Authorization'] = f"Bearer {self.access_token}"
            kwargs['headers'] = headers
            
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except Exception as e:
            return None
            
    def log_test(self, category: str, endpoint: str, method: str, status_code: int, 
                 success: bool, details: str = "", error: str = ""):
        """记录测试结果"""
        result = {
            "category": category,
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "details": details,
            "error": error,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
    def test_all_endpoints(self):
        """测试所有端点"""
        print("🚀 开始全面API测试")
        
        # 1. 健康检查端点
        self.test_health_endpoints()
        
        # 2. 认证端点
        self.test_auth_endpoints()
        
        # 3. 用户管理端点
        self.test_user_endpoints()
        
        # 4. 文档管理端点
        self.test_document_endpoints()
        
        # 5. 主题管理端点
        self.test_topic_endpoints()
        
        # 6. 对话管理端点
        self.test_conversation_endpoints()
        
        # 7. 搜索端点
        self.test_search_endpoints()
        
        # 8. AI服务端点
        self.test_ai_endpoints()
        
        # 9. 集成端点
        self.test_integration_endpoints()
        
    def test_health_endpoints(self):
        """测试健康检查端点"""
        endpoints = [
            "/utils/health-check/",
            "/embedding/health",
            "/llm/health",
            "/search/health",
            "/integration/health",
            "/integration/manticore/health"
        ]
        
        for endpoint in endpoints:
            response = self.make_request("GET", endpoint)
            if response:
                success = response.status_code == 200
                self.log_test("健康检查", endpoint, "GET", response.status_code, success)
                
    def test_auth_endpoints(self):
        """测试认证端点"""
        # Token验证
        if self.access_token:
            response = self.make_request("POST", "/login/test-token")
            if response:
                success = response.status_code == 200
                self.log_test("认证", "/login/test-token", "POST", response.status_code, success)
                
    def test_user_endpoints(self):
        """测试用户端点"""
        if not self.access_token:
            return
            
        endpoints = [
            ("/users/me", "GET"),
            ("/users/", "GET"),
        ]
        
        for endpoint, method in endpoints:
            response = self.make_request(method, endpoint)
            if response:
                success = response.status_code in [200, 403]
                self.log_test("用户管理", endpoint, method, response.status_code, success)
                
        # 用户注册
        signup_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = self.make_request("POST", "/users/signup", json=signup_data)
        if response:
            success = response.status_code in [200, 201, 400]
            self.log_test("用户管理", "/users/signup", "POST", response.status_code, success)
            
    def test_document_endpoints(self):
        """测试文档端点"""
        if not self.access_token:
            return
            
        # 获取文档列表
        response = self.make_request("GET", "/documents/")
        if response:
            success = response.status_code == 200
            self.log_test("文档管理", "/documents/", "GET", response.status_code, success)
            
        # 尝试创建文档
        test_content = "测试文档内容"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
            
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('test.txt', f, 'text/plain')}
                data = {'title': 'Test Document', 'description': 'Test'}
                
                response = self.make_request("POST", "/documents/", files=files, data=data)
                if response:
                    success = response.status_code in [200, 201, 422]
                    details = "创建成功" if success and response.status_code in [200, 201] else "验证错误或其他"
                    self.log_test("文档管理", "/documents/", "POST", response.status_code, success, details)
                else:
                    self.log_test("文档管理", "/documents/", "POST", 0, False, "请求失败")
                    
        finally:
            os.unlink(temp_file_path)
            
    def test_topic_endpoints(self):
        """测试主题端点"""
        if not self.access_token:
            return
            
        # 获取主题列表
        response = self.make_request("GET", "/topics/")
        if response:
            success = response.status_code == 200
            self.log_test("主题管理", "/topics/", "GET", response.status_code, success)
            
        # 获取用户主题统计
        response = self.make_request("GET", "/topics/users/me/stats")
        if response:
            success = response.status_code == 200
            self.log_test("主题管理", "/topics/users/me/stats", "GET", response.status_code, success)
            
        # 尝试创建主题
        topic_data = {
            "title": f"测试主题_{int(time.time())}",
            "description": "API测试主题"
        }
        response = self.make_request("POST", "/topics/", json=topic_data)
        if response:
            success = response.status_code in [200, 201, 422]
            details = "创建成功" if success and response.status_code in [200, 201] else "验证错误或其他"
            self.log_test("主题管理", "/topics/", "POST", response.status_code, success, details)
        else:
            self.log_test("主题管理", "/topics/", "POST", 0, False, "请求失败")
            
    def test_conversation_endpoints(self):
        """测试对话端点"""
        if not self.access_token:
            return
            
        # 获取对话列表
        response = self.make_request("GET", "/conversations/")
        if response:
            success = response.status_code == 200
            self.log_test("对话管理", "/conversations/", "GET", response.status_code, success)
            
        # 创建对话
        conv_data = {
            "title": f"测试对话_{int(time.time())}"
        }
        response = self.make_request("POST", "/conversations/", json=conv_data)
        if response:
            success = response.status_code in [200, 201]
            self.log_test("对话管理", "/conversations/", "POST", response.status_code, success)
            
    def test_search_endpoints(self):
        """测试搜索端点"""
        endpoints = [
            ("/search/initialize", "POST"),
            ("/search/stats", "GET"),
        ]
        
        for endpoint, method in endpoints:
            response = self.make_request(method, endpoint)
            if response:
                success = response.status_code in [200, 201, 500]  # 500可能是Manticore问题
                self.log_test("搜索服务", endpoint, method, response.status_code, success)
                
        # 搜索测试
        search_data = {"query": "test", "limit": 5}
        for endpoint in ["/search/documents", "/search/documents/semantic"]:
            response = self.make_request("POST", endpoint, json=search_data)
            if response:
                success = response.status_code in [200, 500]  # 500可能是Manticore问题
                self.log_test("搜索服务", endpoint, "POST", response.status_code, success)
                
    def test_ai_endpoints(self):
        """测试AI服务端点"""
        # 嵌入服务
        embed_data = {"text": "测试文本"}
        response = self.make_request("POST", "/embedding/embed", json=embed_data)
        if response:
            success = response.status_code == 200
            self.log_test("AI服务", "/embedding/embed", "POST", response.status_code, success)
            
        # LLM服务
        llm_data = {"prompt": "Hello", "max_tokens": 10}
        response = self.make_request("POST", "/llm/generate", json=llm_data)
        if response:
            success = response.status_code == 200
            self.log_test("AI服务", "/llm/generate", "POST", response.status_code, success)
            
    def test_integration_endpoints(self):
        """测试集成端点"""
        # Dramatiq测试
        response = self.make_request("POST", "/integration/dramatiq/hello")
        if response:
            success = response.status_code in [200, 202]
            self.log_test("集成服务", "/integration/dramatiq/hello", "POST", response.status_code, success)
            
        # 全集成测试
        response = self.make_request("GET", "/integration/test/full-integration")
        if response:
            success = response.status_code == 200
            self.log_test("集成服务", "/integration/test/full-integration", "GET", response.status_code, success)
            
    def generate_report(self):
        """生成测试报告"""
        end_time = time.time()
        duration = end_time - self.start_time
        
        print("\n" + "="*80)
        print("📊 Master-Know API 测试报告")
        print("="*80)
        print(f"🕐 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏱️  测试耗时: {duration:.2f} 秒")
        print(f"🌐 测试目标: {BASE_URL}")
        
        # 统计信息
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"\n📈 总体统计:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过: {passed_tests} ✅")
        print(f"   失败: {failed_tests} ❌")
        print(f"   成功率: {(passed_tests/total_tests*100):.1f}%")
        
        # 按分类统计
        categories = {}
        for result in self.test_results:
            cat = result['category']
            if cat not in categories:
                categories[cat] = {'total': 0, 'passed': 0}
            categories[cat]['total'] += 1
            if result['success']:
                categories[cat]['passed'] += 1
                
        print(f"\n📋 分类统计:")
        for category, stats in categories.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            print(f"   {category}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
            
        # 失败的测试
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['method']} {result['endpoint']} ({result['status_code']}) - {result['category']}")
                    if result['details']:
                        print(f"     详情: {result['details']}")
                        
        # 状态码分布
        status_codes = {}
        for result in self.test_results:
            code = result['status_code']
            status_codes[code] = status_codes.get(code, 0) + 1
            
        print(f"\n📊 状态码分布:")
        for code, count in sorted(status_codes.items()):
            print(f"   {code}: {count} 次")
            
        print("\n" + "="*80)
        
        # 生成JSON报告
        report_data = {
            "timestamp": datetime.now().isoformat(),
            "duration": duration,
            "base_url": BASE_URL,
            "summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": (passed_tests/total_tests*100) if total_tests > 0 else 0
            },
            "categories": categories,
            "status_codes": status_codes,
            "test_results": self.test_results
        }
        
        with open("api_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
            
        print(f"📄 详细报告已保存到: api_test_report.json")
        
        return failed_tests == 0
        
    def run_complete_test(self):
        """运行完整测试"""
        if not self.authenticate():
            print("❌ 认证失败，无法继续测试")
            return False
            
        self.test_all_endpoints()
        return self.generate_report()

if __name__ == "__main__":
    tester = FinalAPITestReport()
    success = tester.run_complete_test()
    sys.exit(0 if success else 1)
