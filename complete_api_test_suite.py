#!/usr/bin/env python3
"""
Master-Know API 完整测试套件
基于实际的OpenAPI规范测试所有端点
"""

import requests
import json
import sys
import time
from typing import Dict, Any, Optional, List
import tempfile
import os
from urllib.parse import urljoin

# 配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

class CompleteAPITestSuite:
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = 30
        self.access_token = None
        self.test_results = []
        self.user_id = None
        self.document_id = None
        self.topic_id = None
        self.conversation_id = None
        self.item_id = None
        
    def log_test(self, endpoint: str, method: str, status_code: int, success: bool, 
                 details: str = "", error: str = ""):
        """记录测试结果"""
        result = {
            "endpoint": endpoint,
            "method": method,
            "status_code": status_code,
            "success": success,
            "details": details,
            "error": error
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {method} {endpoint} - {status_code} - {details}")
        if error:
            print(f"   Error: {error}")
        
    def make_request(self, method: str, endpoint: str, **kwargs) -> Optional[requests.Response]:
        """发送请求"""
        url = f"{API_BASE}{endpoint}"
        
        if self.access_token:
            headers = kwargs.get('headers', {})
            headers['Authorization'] = f"Bearer {self.access_token}"
            kwargs['headers'] = headers
            
        try:
            response = self.session.request(method, url, **kwargs)
            return response
        except Exception as e:
            self.log_test(endpoint, method, 0, False, "Request failed", str(e))
            return None
            
    def authenticate(self):
        """认证获取token"""
        print("🔐 认证...")
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        response = self.make_request("POST", "/login/access-token", data=login_data)
        if response and response.status_code == 200:
            data = response.json()
            self.access_token = data.get("access_token")
            print(f"✅ 认证成功")
            return True
        else:
            print(f"❌ 认证失败")
            return False
            
    def test_all_health_endpoints(self):
        """测试所有健康检查端点"""
        print("\n=== 健康检查端点 ===")
        
        endpoints = [
            "/utils/health-check/",
            "/embedding/health",
            "/llm/health", 
            "/search/health",
            "/integration/health",
            "/integration/manticore/health"
        ]
        
        for endpoint in endpoints:
            response = self.make_request("GET", endpoint)
            if response:
                success = response.status_code == 200
                self.log_test(endpoint, "GET", response.status_code, success)
                
    def test_authentication_endpoints(self):
        """测试认证相关端点"""
        print("\n=== 认证端点 ===")
        
        # Token验证
        if self.access_token:
            response = self.make_request("POST", "/login/test-token")
            if response:
                success = response.status_code == 200
                self.log_test("/login/test-token", "POST", response.status_code, success)
                
    def test_user_endpoints(self):
        """测试用户端点"""
        print("\n=== 用户端点 ===")
        
        if not self.access_token:
            return
            
        # 获取当前用户
        response = self.make_request("GET", "/users/me")
        if response and response.status_code == 200:
            user_data = response.json()
            self.user_id = user_data.get("id")
            self.log_test("/users/me", "GET", response.status_code, True)
        else:
            self.log_test("/users/me", "GET", response.status_code if response else 0, False)
            
        # 获取用户列表
        response = self.make_request("GET", "/users/")
        if response:
            success = response.status_code in [200, 403]
            self.log_test("/users/", "GET", response.status_code, success)
            
        # 用户注册
        signup_data = {
            "email": f"test_{int(time.time())}@example.com",
            "password": "testpassword123",
            "full_name": "Test User"
        }
        response = self.make_request("POST", "/users/signup", json=signup_data)
        if response:
            success = response.status_code in [200, 201, 400]
            self.log_test("/users/signup", "POST", response.status_code, success)
            
        # 修改密码
        password_data = {
            "current_password": "changethis",
            "new_password": "newpassword123"
        }
        response = self.make_request("PATCH", "/users/me/password", json=password_data)
        if response:
            success = response.status_code in [200, 400, 422]  # 可能验证失败
            self.log_test("/users/me/password", "PATCH", response.status_code, success)
            
        # 获取特定用户（如果有用户ID）
        if self.user_id:
            response = self.make_request("GET", f"/users/{self.user_id}")
            if response:
                success = response.status_code in [200, 403]
                self.log_test(f"/users/{self.user_id}", "GET", response.status_code, success)
                
    def test_document_endpoints(self):
        """测试文档端点"""
        print("\n=== 文档端点 ===")
        
        if not self.access_token:
            return
            
        # 获取文档列表
        response = self.make_request("GET", "/documents/")
        if response:
            success = response.status_code == 200
            self.log_test("/documents/", "GET", response.status_code, success)
            
        # 创建文档
        test_content = "这是一个测试文档，用于API测试。包含中文内容。"
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as f:
            f.write(test_content)
            temp_file_path = f.name
            
        try:
            with open(temp_file_path, 'rb') as f:
                files = {'file': ('test.txt', f, 'text/plain')}
                data = {'title': 'Test Document', 'description': 'Test document'}
                
                response = self.make_request("POST", "/documents/", files=files, data=data)
                if response and response.status_code in [200, 201]:
                    doc_data = response.json()
                    self.document_id = doc_data.get("id")
                    self.log_test("/documents/", "POST", response.status_code, True)
                else:
                    self.log_test("/documents/", "POST", response.status_code if response else 0, False)
                    
        finally:
            os.unlink(temp_file_path)
            
        # 测试文档相关操作
        if self.document_id:
            endpoints = [
                (f"/documents/{self.document_id}", "GET"),
                (f"/documents/{self.document_id}/chunks", "GET"),
                (f"/documents/{self.document_id}/stats", "GET"),
                (f"/documents/{self.document_id}/process", "POST"),
                (f"/documents/{self.document_id}/reprocess", "POST"),
            ]
            
            for endpoint, method in endpoints:
                response = self.make_request(method, endpoint)
                if response:
                    success = response.status_code in [200, 202, 404]
                    self.log_test(endpoint, method, response.status_code, success)
                    
    def test_embedding_endpoints(self):
        """测试嵌入端点"""
        print("\n=== 嵌入端点 ===")
        
        # 单个文本嵌入
        embed_data = {"text": "测试文本"}
        response = self.make_request("POST", "/embedding/embed", json=embed_data)
        if response:
            success = response.status_code == 200
            self.log_test("/embedding/embed", "POST", response.status_code, success)
            
        # 批量嵌入
        batch_data = {"texts": ["文本1", "文本2", "文本3"]}
        response = self.make_request("POST", "/embedding/embed/batch", json=batch_data)
        if response:
            success = response.status_code == 200
            self.log_test("/embedding/embed/batch", "POST", response.status_code, success)
            
    def test_llm_endpoints(self):
        """测试LLM端点"""
        print("\n=== LLM端点 ===")
        
        # 生成文本
        llm_data = {
            "prompt": "请简单介绍一下人工智能",
            "max_tokens": 50
        }
        response = self.make_request("POST", "/llm/generate", json=llm_data)
        if response:
            success = response.status_code == 200
            self.log_test("/llm/generate", "POST", response.status_code, success)
            
    def test_search_endpoints(self):
        """测试搜索端点"""
        print("\n=== 搜索端点 ===")
        
        # 初始化搜索
        response = self.make_request("POST", "/search/initialize")
        if response:
            success = response.status_code in [200, 201]
            self.log_test("/search/initialize", "POST", response.status_code, success)
            
        # 文档搜索
        search_data = {"query": "测试", "limit": 5}
        response = self.make_request("POST", "/search/documents", json=search_data)
        if response:
            success = response.status_code == 200
            self.log_test("/search/documents", "POST", response.status_code, success)
            
        # 语义搜索
        response = self.make_request("POST", "/search/documents/semantic", json=search_data)
        if response:
            success = response.status_code == 200
            self.log_test("/search/documents/semantic", "POST", response.status_code, success)
            
        # 搜索统计
        response = self.make_request("GET", "/search/stats")
        if response:
            success = response.status_code == 200
            self.log_test("/search/stats", "GET", response.status_code, success)
            
        # 索引文档（如果有文档ID）
        if self.document_id:
            response = self.make_request("POST", f"/search/index/document/{self.document_id}")
            if response:
                success = response.status_code in [200, 202]
                self.log_test(f"/search/index/document/{self.document_id}", "POST", response.status_code, success)
                
    def test_topic_endpoints(self):
        """测试主题端点"""
        print("\n=== 主题端点 ===")
        
        if not self.access_token:
            return
            
        # 获取主题列表
        response = self.make_request("GET", "/topics/")
        if response:
            success = response.status_code == 200
            self.log_test("/topics/", "GET", response.status_code, success)
            
        # 创建主题
        topic_data = {
            "title": "测试主题",
            "description": "API测试主题"
        }
        response = self.make_request("POST", "/topics/", json=topic_data)
        if response and response.status_code in [200, 201]:
            topic_data = response.json()
            self.topic_id = topic_data.get("id")
            self.log_test("/topics/", "POST", response.status_code, True)
        else:
            self.log_test("/topics/", "POST", response.status_code if response else 0, False)
            
        # 用户主题统计
        response = self.make_request("GET", "/topics/users/me/stats")
        if response:
            success = response.status_code == 200
            self.log_test("/topics/users/me/stats", "GET", response.status_code, success)
            
        # 主题相关操作
        if self.topic_id:
            endpoints = [
                (f"/topics/{self.topic_id}", "GET"),
                (f"/topics/{self.topic_id}/stats", "GET"),
                (f"/topics/{self.topic_id}/documents", "GET"),
            ]
            
            for endpoint, method in endpoints:
                response = self.make_request(method, endpoint)
                if response:
                    success = response.status_code == 200
                    self.log_test(endpoint, method, response.status_code, success)
                    
            # 关联文档到主题
            if self.document_id:
                response = self.make_request("POST", f"/topics/{self.topic_id}/documents/{self.document_id}")
                if response:
                    success = response.status_code in [200, 201, 409]  # 409可能已存在
                    self.log_test(f"/topics/{self.topic_id}/documents/{self.document_id}", "POST", response.status_code, success)
                    
    def run_complete_test_suite(self):
        """运行完整测试套件"""
        print("🚀 Master-Know API 完整测试套件")
        print(f"📍 测试目标: {BASE_URL}")
        print(f"⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        start_time = time.time()
        
        # 认证
        if not self.authenticate():
            print("❌ 认证失败，无法继续测试")
            return False
            
        # 执行所有测试
        self.test_all_health_endpoints()
        self.test_authentication_endpoints()
        self.test_user_endpoints()
        self.test_document_endpoints()
        self.test_embedding_endpoints()
        self.test_llm_endpoints()
        self.test_search_endpoints()
        self.test_topic_endpoints()
        
        # 输出总结
        end_time = time.time()
        return self.print_final_summary(end_time - start_time)
        
    def print_final_summary(self, duration: float):
        """打印最终总结"""
        print("\n" + "="*80)
        print("📊 完整测试套件总结")
        print("="*80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"⏱️  测试耗时: {duration:.2f} 秒")
        print(f"📈 总测试数: {total_tests}")
        print(f"✅ 通过: {passed_tests}")
        print(f"❌ 失败: {failed_tests}")
        print(f"📊 成功率: {(passed_tests/total_tests*100):.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   • {result['method']} {result['endpoint']} ({result['status_code']})")
                    if result['error']:
                        print(f"     Error: {result['error']}")
                        
        print("\n" + "="*80)
        return failed_tests == 0

if __name__ == "__main__":
    tester = CompleteAPITestSuite()
    success = tester.run_complete_test_suite()
    sys.exit(0 if success else 1)
