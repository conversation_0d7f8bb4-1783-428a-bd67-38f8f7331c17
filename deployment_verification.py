#!/usr/bin/env python3
"""
Master-Know 部署验证脚本
验证所有服务是否正确部署和运行
"""

import requests
import subprocess
import json
import time
from typing import Dict, List, Tuple

class DeploymentVerifier:
    def __init__(self):
        self.base_url = "http://localhost:8000"
        self.frontend_url = "http://localhost:5173"
        self.results = []
        
    def log_result(self, service: str, check: str, status: bool, details: str = ""):
        """记录验证结果"""
        result = {
            "service": service,
            "check": check,
            "status": status,
            "details": details
        }
        self.results.append(result)
        
        status_icon = "✅" if status else "❌"
        print(f"{status_icon} {service} - {check}: {details}")
        
    def check_docker_services(self):
        """检查Docker服务状态"""
        print("\n🐳 检查Docker服务状态")
        
        try:
            result = subprocess.run(
                ["docker", "compose", "ps", "--format", "json"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                services = []
                for line in result.stdout.strip().split('\n'):
                    if line:
                        try:
                            service_info = json.loads(line)
                            services.append(service_info)
                        except json.JSONDecodeError:
                            continue
                
                # 检查关键服务
                required_services = [
                    "master-know-backend-1",
                    "master-know-frontend-1", 
                    "master-know-db-1",
                    "master-know-redis-1",
                    "master-know-dramatiq-worker-1"
                ]
                
                for service_name in required_services:
                    service_found = False
                    for service in services:
                        if service.get("Name") == service_name:
                            service_found = True
                            status = service.get("State", "").lower()
                            health = service.get("Health", "")
                            
                            if status == "running":
                                if health and health != "healthy":
                                    self.log_result("Docker", service_name, False, f"运行中但健康检查失败: {health}")
                                else:
                                    self.log_result("Docker", service_name, True, "运行正常")
                            else:
                                self.log_result("Docker", service_name, False, f"状态异常: {status}")
                            break
                    
                    if not service_found:
                        self.log_result("Docker", service_name, False, "服务未找到")
                        
            else:
                self.log_result("Docker", "服务检查", False, f"命令执行失败: {result.stderr}")
                
        except Exception as e:
            self.log_result("Docker", "服务检查", False, f"异常: {e}")
            
    def check_api_endpoints(self):
        """检查API端点"""
        print("\n🌐 检查API端点")
        
        # 关键端点检查
        endpoints = [
            ("/api/v1/utils/health-check/", "健康检查"),
            ("/docs", "API文档"),
            ("/api/v1/openapi.json", "OpenAPI规范")
        ]
        
        for endpoint, description in endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    self.log_result("API", description, True, f"状态码: {response.status_code}")
                else:
                    self.log_result("API", description, False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_result("API", description, False, f"连接失败: {e}")
                
    def check_authentication(self):
        """检查认证功能"""
        print("\n🔐 检查认证功能")
        
        login_data = {
            "username": "<EMAIL>",
            "password": "changethis"
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/api/v1/login/access-token",
                data=login_data,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                token = data.get("access_token")
                if token:
                    self.log_result("认证", "管理员登录", True, "获取到访问令牌")
                    
                    # 验证token
                    headers = {"Authorization": f"Bearer {token}"}
                    response = requests.post(
                        f"{self.base_url}/api/v1/login/test-token",
                        headers=headers,
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        self.log_result("认证", "令牌验证", True, "令牌有效")
                    else:
                        self.log_result("认证", "令牌验证", False, f"验证失败: {response.status_code}")
                else:
                    self.log_result("认证", "管理员登录", False, "未获取到令牌")
            else:
                self.log_result("认证", "管理员登录", False, f"登录失败: {response.status_code}")
                
        except Exception as e:
            self.log_result("认证", "管理员登录", False, f"连接异常: {e}")
            
    def check_frontend(self):
        """检查前端服务"""
        print("\n🖥️  检查前端服务")
        
        try:
            response = requests.get(self.frontend_url, timeout=10)
            if response.status_code == 200:
                self.log_result("前端", "页面访问", True, f"状态码: {response.status_code}")
                
                # 检查是否是HTML页面
                if "text/html" in response.headers.get("content-type", ""):
                    self.log_result("前端", "内容类型", True, "HTML页面")
                else:
                    self.log_result("前端", "内容类型", False, f"非HTML: {response.headers.get('content-type')}")
            else:
                self.log_result("前端", "页面访问", False, f"状态码: {response.status_code}")
                
        except Exception as e:
            self.log_result("前端", "页面访问", False, f"连接异常: {e}")
            
    def check_database_connection(self):
        """检查数据库连接"""
        print("\n🗄️  检查数据库连接")
        
        try:
            result = subprocess.run(
                ["docker", "exec", "master-know-db-1", "pg_isready", "-U", "master_know_user"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                self.log_result("数据库", "连接检查", True, "PostgreSQL连接正常")
            else:
                self.log_result("数据库", "连接检查", False, f"连接失败: {result.stderr}")
                
        except Exception as e:
            self.log_result("数据库", "连接检查", False, f"检查异常: {e}")
            
    def check_redis_connection(self):
        """检查Redis连接"""
        print("\n📦 检查Redis连接")
        
        try:
            result = subprocess.run(
                ["docker", "exec", "master-know-redis-1", "redis-cli", "ping"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0 and "PONG" in result.stdout:
                self.log_result("Redis", "连接检查", True, "Redis连接正常")
            else:
                self.log_result("Redis", "连接检查", False, f"连接失败: {result.stdout}")
                
        except Exception as e:
            self.log_result("Redis", "连接检查", False, f"检查异常: {e}")
            
    def check_ai_services(self):
        """检查AI服务"""
        print("\n🤖 检查AI服务")
        
        # 检查嵌入服务
        try:
            response = requests.get(f"{self.base_url}/api/v1/embedding/health", timeout=10)
            if response.status_code == 200:
                self.log_result("AI服务", "嵌入服务", True, "健康检查通过")
            else:
                self.log_result("AI服务", "嵌入服务", False, f"健康检查失败: {response.status_code}")
        except Exception as e:
            self.log_result("AI服务", "嵌入服务", False, f"连接异常: {e}")
            
        # 检查LLM服务
        try:
            response = requests.get(f"{self.base_url}/api/v1/llm/health", timeout=10)
            if response.status_code == 200:
                self.log_result("AI服务", "LLM服务", True, "健康检查通过")
            else:
                self.log_result("AI服务", "LLM服务", False, f"健康检查失败: {response.status_code}")
        except Exception as e:
            self.log_result("AI服务", "LLM服务", False, f"连接异常: {e}")
            
    def generate_summary(self):
        """生成验证总结"""
        print("\n" + "="*60)
        print("📋 部署验证总结")
        print("="*60)
        
        total_checks = len(self.results)
        passed_checks = sum(1 for r in self.results if r['status'])
        failed_checks = total_checks - passed_checks
        
        print(f"总检查项: {total_checks}")
        print(f"通过: {passed_checks} ✅")
        print(f"失败: {failed_checks} ❌")
        print(f"成功率: {(passed_checks/total_checks*100):.1f}%")
        
        # 按服务分组
        services = {}
        for result in self.results:
            service = result['service']
            if service not in services:
                services[service] = {'total': 0, 'passed': 0}
            services[service]['total'] += 1
            if result['status']:
                services[service]['passed'] += 1
                
        print(f"\n📊 各服务状态:")
        for service, stats in services.items():
            success_rate = (stats['passed'] / stats['total'] * 100) if stats['total'] > 0 else 0
            status_icon = "✅" if success_rate == 100 else "⚠️" if success_rate >= 50 else "❌"
            print(f"   {status_icon} {service}: {stats['passed']}/{stats['total']} ({success_rate:.1f}%)")
            
        if failed_checks > 0:
            print(f"\n❌ 失败的检查项:")
            for result in self.results:
                if not result['status']:
                    print(f"   • {result['service']} - {result['check']}: {result['details']}")
                    
        print("\n" + "="*60)
        
        # 部署状态判断
        if failed_checks == 0:
            print("🎉 部署验证完全通过！系统已准备就绪。")
            return True
        elif passed_checks / total_checks >= 0.8:
            print("⚠️  部署基本成功，但有少量问题需要修复。")
            return True
        else:
            print("❌ 部署存在严重问题，需要立即修复。")
            return False
            
    def run_verification(self):
        """运行完整的部署验证"""
        print("🚀 开始Master-Know部署验证")
        print(f"⏰ 验证时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        self.check_docker_services()
        self.check_api_endpoints()
        self.check_authentication()
        self.check_frontend()
        self.check_database_connection()
        self.check_redis_connection()
        self.check_ai_services()
        
        return self.generate_summary()

if __name__ == "__main__":
    verifier = DeploymentVerifier()
    success = verifier.run_verification()
    
    if success:
        print("\n🌟 部署验证成功！可以开始使用Master-Know系统。")
        print(f"📱 前端地址: http://localhost:5173")
        print(f"📚 API文档: http://localhost:8000/docs")
    else:
        print("\n🔧 请修复上述问题后重新验证。")
        
    exit(0 if success else 1)
